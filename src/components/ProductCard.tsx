import React, { useState, useRef, useEffect } from 'react';
import { Star, ShoppingCart, Plus, Minus, Eye, Heart, QrCode, Download, Sparkles, Award } from 'lucide-react';

// Mock implementations for demo purposes
const useNavigate = () => (path) => console.log(`Navigate to: ${path}`);
const useCart = () => ({
  addToCart: async (id, qty) => console.log(`Added ${qty} of product ${id} to cart`),
  loading: false
});
const useAuth = () => ({ isAuthenticated: true });

// Mock JsBarcode for demo
const JsBarcode = (element, code, options) => {
  if (element) {
    element.innerHTML = `
      <rect width="180" height="60" fill="${options.background}"/>
      <text x="90" y="35" text-anchor="middle" fill="${options.lineColor}" font-size="12" font-family="monospace">${code}</text>
    `;
  }
};

// Mock components
const Card = ({ children, className }) => (
  <div className={`rounded-lg border ${className}`}>{children}</div>
);
const CardContent = ({ children, className }) => (
  <div className={className}>{children}</div>
);
const CardFooter = ({ children, className }) => (
  <div className={className}>{children}</div>
);
const Button = ({ children, className, onClick, disabled, variant, title, ...props }) => (
  <button
    className={`inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 disabled:opacity-50 disabled:pointer-events-none ${className}`}
    onClick={onClick}
    disabled={disabled}
    title={title}
    {...props}
  >
    {children}
  </button>
);
const Badge = ({ children, className, variant }) => (
  <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${className}`}>
    {children}
  </span>
);

interface Product {
  id: number;
  name: string;
  description: string;
  price: string;
  offer_price?: string;
  is_on_offer: boolean;
  in_stock?: boolean;
  rating: number;
  image_url?: string;
  image?: string;
  category?: { name: string };
  category_name?: string;
  ean_13_code?: string;
}

interface ProductCardProps {
  product: Product;
  onViewDetails?: (product: Product) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  const [showBarcode, setShowBarcode] = useState(false);
  const [quantity, setQuantity] = useState(1);
  const [isFavorite, setIsFavorite] = useState(false);
  const [isFlipped, setIsFlipped] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const barcodeRef = useRef<SVGSVGElement>(null);
  const navigate = useNavigate();
  const { addToCart, loading: cartLoading } = useCart();
  const { isAuthenticated } = useAuth();

  const generateEAN13Code = () => {
    if (product.ean_13_code) {
      return product.ean_13_code;
    }

    const country = "99";
    const company = "12345";
    const productCode = product.id.toString().padStart(5, '0');
    const code = country + company + productCode;
    let sum = 0;
    for (let i = 0; i < 12; i++) {
      sum += parseInt(code[i]) * (i % 2 === 0 ? 1 : 3);
    }
    const checkDigit = (10 - (sum % 10)) % 10;
    return code + checkDigit;
  };

  useEffect(() => {
    if (showBarcode && barcodeRef.current && isFlipped) {
      const eanCode = generateEAN13Code();
      JsBarcode(barcodeRef.current, eanCode, {
        format: "EAN13",
        width: 1.8,
        height: 50,
        displayValue: true,
        fontSize: 11,
        textMargin: 4,
        background: "#ffffff",
        lineColor: "#e34d69"
      });
    }
  }, [showBarcode, isFlipped, product.id, product.ean_13_code]);

  const handleAddToCart = async () => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }
    await addToCart(product.id, quantity);
  };

  const handleBarcodeToggle = () => {
    setShowBarcode(!showBarcode);
    setIsFlipped(!isFlipped);
  };

  const isInStock = product.in_stock ?? true;
  const getCategoryName = () => {
    if (typeof product.category === 'object' && product.category?.name) {
      return product.category.name;
    }
    return product.category_name || '';
  };

  const currentPrice = product.is_on_offer && product.offer_price ? product.offer_price : product.price;

  const getProductImage = () => {
    if (product.image_url || product.image) {
      return product.image_url || product.image;
    }
    const categoryImages: { [key: string]: string } = {
      'Skincare': 'https://images.unsplash.com/photo-1556228720-195a672e8a03?w=400&h=400&fit=crop',
      'Makeup': 'https://images.unsplash.com/photo-1586495777744-4413f21062fa?w=400&h=400&fit=crop',
      'Hair Care': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop',
    };
    return categoryImages[getCategoryName()] || 'https://images.unsplash.com/photo-1556228720-195a672e8a03?w=400&h=400&fit=crop';
  };

  const downloadBarcode = () => {
    if (barcodeRef.current) {
      const svgData = new XMLSerializer().serializeToString(barcodeRef.current);
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx?.drawImage(img, 0, 0);
        const pngFile = canvas.toDataURL('image/png');
        const downloadLink = document.createElement('a');
        downloadLink.download = `${product.name}-EAN13-${generateEAN13Code()}.png`;
        downloadLink.href = pngFile;
        downloadLink.click();
      };
      img.src = 'data:image/svg+xml;base64,' + btoa(svgData);
    }
  };

  const discountPercentage = product.is_on_offer && product.offer_price
    ? Math.round(((parseFloat(product.price) - parseFloat(product.offer_price)) / parseFloat(product.price)) * 100)
    : 0;

  return (
    <div className="group relative h-full">
      <div
        className={`relative w-full h-full transition-transform duration-700 transform-gpu ${isFlipped ? '[transform:rotateY(180deg)]' : ''
          }`}
        style={{ transformStyle: 'preserve-3d' }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Front Side */}
        <Card className={`relative bg-white border-0 shadow-lg hover:shadow-2xl transition-all duration-500 rounded-3xl overflow-hidden h-full backdrop-blur-xl bg-gradient-to-br from-white via-white to-orange-50/30 ${isFlipped ? '[backface-visibility:hidden]' : ''} ${isHovered ? 'scale-105' : ''}`}>

          {/* Premium Image Section */}
          <div className="relative h-64 overflow-hidden">
            {/* Image with sophisticated overlay */}
            <div className="relative h-full">
              <img
                src={getProductImage()}
                alt={product.name}
                className={`w-full h-full object-cover transition-all duration-700 ${imageLoaded ? 'scale-100' : 'scale-110'} ${isHovered ? 'scale-110' : 'scale-100'}`}
                onLoad={() => setImageLoaded(true)}
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = 'https://images.unsplash.com/photo-1556228720-195a672e8a03?w=400&h=400&fit=crop';
                }}
              />

              {/* Gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </div>

            {/* Top Status Badges */}
            <div className="absolute top-4 left-4 flex flex-col gap-2">
              {!isInStock && (
                <Badge
                  variant="destructive"
                  className="bg-red-500/90 backdrop-blur-sm text-white text-xs font-medium shadow-lg">
                  Sold Out
                </Badge>
              )}
              {product.is_on_offer && (
                <Badge
                  variant="destructive"
                  className="bg-gradient-to-r from-emerald-500 to-emerald-600 text-white text-xs font-medium shadow-lg animate-pulse">
                  <Sparkles className="w-3 h-3 mr-1" />
                  -{discountPercentage}% OFF
                </Badge>
              )}
              {product.rating >= 4.5 && (
                <Badge
                  variant="destructive"
                  className="bg-gradient-to-r from-amber-400 to-amber-500 text-white text-xs font-medium shadow-lg">
                  <Award className="w-3 h-3 mr-1" />
                  Premium
                </Badge>
              )}
            </div>

            {/* Action Icons */}
            <div className="absolute top-4 right-4 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300 translate-x-2 group-hover:translate-x-0">
              <button
                onClick={() => setIsFavorite(!isFavorite)}
                className={`w-10 h-10 backdrop-blur-xl rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 ${isFavorite
                  ? 'bg-rose-500 text-white'
                  : 'bg-white/90 text-gray-600 hover:text-rose-500'
                  }`}
              >
                <Heart className={`w-4 h-4 transition-all duration-300 ${isFavorite ? 'fill-current scale-110' : ''}`} />
              </button>
              <button
                onClick={handleBarcodeToggle}
                className="w-10 h-10 bg-white/90 backdrop-blur-xl rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 text-gray-600 hover:text-rose-500"
              >
                <QrCode className="w-4 h-4" />
              </button>
            </div>

            {/* Category & Rating Overlay */}
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent p-4">
              <div className="flex justify-between items-end text-white">
                <div className="text-xs font-light uppercase tracking-widest opacity-90">
                  {getCategoryName()}
                </div>
                <div className="flex items-center gap-1 bg-white/20 backdrop-blur-sm px-3 py-1.5 rounded-full">
                  <Star className="w-3 h-3 fill-amber-400 text-amber-400" />
                  <span className="text-xs font-medium">{product.rating}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Content Section */}
          <CardContent className="p-6 space-y-4 flex-1 flex flex-col">
            {/* Product Name */}
            <h3 className="font-semibold text-lg text-gray-900 leading-tight line-clamp-2 mb-2">
              {product.name}
            </h3>

            {/* Description */}
            <p className="text-sm text-gray-600 leading-relaxed line-clamp-2 flex-1">
              {product.description}
            </p>

            {/* Price Section */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-baseline gap-2">
                  <span className="text-2xl font-bold bg-gradient-to-r from-rose-600 to-rose-500 bg-clip-text text-transparent">
                    Rs. {currentPrice}
                  </span>
                  {product.is_on_offer && product.offer_price && (
                    <span className="text-sm text-gray-400 line-through">
                      Rs. {product.price}
                    </span>
                  )}
                </div>
                {product.is_on_offer && product.offer_price && (
                  <div className="text-right">
                    <div className="text-xs text-emerald-600 font-medium">
                      Save Rs. {(parseFloat(product.price) - parseFloat(product.offer_price)).toFixed(0)}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </CardContent>

          {/* Footer Section */}
          <CardFooter className="p-6 pt-0 space-y-4">
            {/* Quantity Selector */}
            {isInStock && (
              <div className="flex items-center justify-center gap-4 bg-gradient-to-r from-orange-50 to-rose-50 rounded-2xl p-4 border border-rose-100">
                <span className="text-sm font-medium text-gray-700 min-w-max">Quantity:</span>
                <div className="flex items-center gap-3">
                  <button
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    disabled={quantity <= 1}
                    className="w-8 h-8 rounded-full bg-white border-2 border-rose-200 flex items-center justify-center disabled:opacity-50 hover:border-rose-300 hover:bg-rose-50 transition-all duration-200 disabled:cursor-not-allowed"
                  >
                    <Minus className="w-3 h-3 text-rose-600" />
                  </button>
                  <span className="font-semibold text-gray-900 min-w-[2rem] text-center text-lg">
                    {quantity}
                  </span>
                  <button
                    onClick={() => setQuantity(quantity + 1)}
                    className="w-8 h-8 rounded-full bg-white border-2 border-rose-200 flex items-center justify-center hover:border-rose-300 hover:bg-rose-50 transition-all duration-200"
                  >
                    <Plus className="w-3 h-3 text-rose-600" />
                  </button>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="grid grid-cols-4 gap-3">
              <Button
                disabled={!isInStock || cartLoading}
                variant="default"
                title={!isInStock ? 'Product is out of stock' : cartLoading ? 'Adding to cart...' : 'Add to cart'}
                className={`col-span-3 h-12 text-sm font-medium rounded-2xl transition-all duration-300 ${!isInStock || cartLoading
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-gradient-to-r from-rose-500 to-rose-600 hover:from-rose-600 hover:to-rose-700 text-white shadow-lg hover:shadow-xl hover:scale-105'
                  }`}
                onClick={handleAddToCart}

              >
                <ShoppingCart className="w-4 h-4 mr-2" />
                {!isInStock ? 'Sold Out' : cartLoading ? 'Adding...' : 'Add to Cart'}
              </Button>
              <Button
                disabled={!isAuthenticated}
                variant="outline"
                className="col-span-1 h-12 border-2 border-rose-200 hover:border-rose-300 hover:bg-rose-50 rounded-2xl transition-all duration-300 hover:scale-105"
                onClick={() => navigate(`/products/${product.id}`)}
                title="View Details"
              >
                <Eye className="w-4 h-4 text-rose-600" />
              </Button>
            </div>
          </CardFooter>
        </Card>

        {/* Back Side - Barcode */}
        <Card className={`absolute inset-0 bg-gradient-to-br from-white via-orange-50 to-rose-50 border-0 shadow-2xl rounded-3xl ${!isFlipped ? '[backface-visibility:hidden]' : ''} [transform:rotateY(180deg)]`}>
          <div className="h-full flex flex-col items-center justify-center p-8 space-y-6">
            {/* Header */}
            <div className="text-center space-y-2">
              <div className="w-16 h-16 bg-gradient-to-br from-rose-500 to-rose-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <QrCode className="w-8 h-8 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900">Product Barcode</h4>
              <p className="text-sm text-gray-600">EAN-13 Code</p>
            </div>

            {/* Barcode Display */}
            <div className="bg-white border-2 border-rose-100 rounded-2xl p-6 shadow-lg">
              <svg ref={barcodeRef} className="max-w-full"></svg>
            </div>

            {/* Code Display */}
            <div className="text-center space-y-2">
              <p className="text-xs text-gray-500 uppercase tracking-wide">Product Code:</p>
              <div className="bg-gray-100 rounded-lg px-4 py-2">
                <p className="font-mono text-sm font-semibold text-gray-900">
                  {generateEAN13Code()}
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 w-full max-w-xs">
              <Button
                title={"Download Barcode"}
                disabled={!isAuthenticated}
                variant="default"
                onClick={downloadBarcode}
                className="flex-1 bg-gradient-to-r from-rose-500 to-rose-600 hover:from-rose-600 hover:to-rose-700 text-white h-12 text-sm font-medium rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <Download className="w-4 h-4 mr-2" />
                Download
              </Button>
              <Button
                disabled={!isAuthenticated}
                title={"Back to Product"}
                onClick={handleBarcodeToggle}
                variant="outline"
                className="flex-1 border-2 border-rose-200 hover:border-rose-300 hover:bg-rose-50 h-12 text-sm font-medium rounded-2xl transition-all duration-300"
              >
                Back
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

// Demo component with sample product
const DemoProductCard = () => {
  const sampleProduct: Product = {
    id: 1,
    name: "Luxurious Rose Gold Serum",
    description: "A premium anti-aging serum infused with 24k gold particles and rose extract for radiant, youthful skin.",
    price: "2500.00",
    offer_price: "1999.00",
    is_on_offer: true,
    in_stock: true,
    rating: 4.8,
    image_url: "https://images.unsplash.com/photo-1556228720-195a672e8a03?w=400&h=400&fit=crop",
    category_name: "Skincare"
  };

  return (
    <div className="max-w-sm mx-auto p-4">
      <ProductCard product={sampleProduct} />
    </div>
  );
};

export default DemoProductCard;